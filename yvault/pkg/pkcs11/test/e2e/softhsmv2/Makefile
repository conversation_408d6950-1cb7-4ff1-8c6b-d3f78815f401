# SoftHSMv2 Build Makefile
# Provides convenient targets for building and managing SoftHSMv2

.PHONY: help check build clean test install setup-env show-config

# Default target
help:
	@echo "SoftHSMv2 Build Targets for PKCS#11 Testing"
	@echo ""
	@echo "Available targets:"
	@echo "  help        - Show this help message"
	@echo "  check       - Check build dependencies"
	@echo "  build       - Build SoftHSMv2 from source"
	@echo "  clean       - Clean build artifacts"
	@echo "  test        - Run build script validation tests"
	@echo "  install     - Build and install SoftHSMv2"
	@echo "  setup-env   - Show environment setup instructions"
	@echo "  show-config - Display current configuration"
	@echo ""
	@echo "Examples:"
	@echo "  make check     # Check if all dependencies are available"
	@echo "  make build     # Build SoftHSMv2"
	@echo "  make clean     # Clean all build artifacts"
	@echo "  make install   # Full build and install"

# Check dependencies
check:
	@echo "Checking build dependencies..."
	./check_dependencies.sh

# Build SoftHSMv2
build:
	@echo "Building SoftHSMv2..."
	./install-softhsmv2.sh

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@if [ -d "build" ]; then \
		echo "Removing build directory..."; \
		rm -rf build; \
	fi
	@if [ -d "temp_build" ]; then \
		echo "Removing temporary build directory..."; \
		rm -rf temp_build; \
	fi
	@echo "Clean completed."

# Run validation tests
test:
	@echo "Running build script validation tests..."
	./test_build_script.sh

# Full install (check + build)
install: check build
	@echo ""
	@echo "SoftHSMv2 installation completed!"
	@echo "Run 'make setup-env' for usage instructions."

# Show environment setup instructions
setup-env:
	@echo "Environment Setup Instructions:"
	@echo ""
	@if [ -f "build/setup_env.sh" ]; then \
		echo "To use SoftHSMv2, run:"; \
		echo "  source build/setup_env.sh"; \
		echo ""; \
		echo "Then initialize a test token:"; \
		echo "  softhsm2-util --init-token --slot 0 --label TestToken"; \
		echo ""; \
		echo "List available slots:"; \
		echo "  softhsm2-util --show-slots"; \
	else \
		echo "SoftHSMv2 not built yet. Run 'make install' first."; \
	fi

# Show current configuration
show-config:
	@echo "Current SoftHSMv2 Configuration:"
	@echo ""
	@echo "Configuration file: softhsm2.conf"
	@if [ -f "softhsm2.conf" ]; then \
		echo "Contents:"; \
		cat softhsm2.conf | sed 's/^/  /'; \
	else \
		echo "Configuration file not found."; \
	fi
	@echo ""
	@if [ -d "test_data" ]; then \
		echo "Token directory: test_data/ (exists)"; \
		echo "Token files:"; \
		ls -la test_data/ 2>/dev/null | sed 's/^/  /' || echo "  (empty)"; \
	else \
		echo "Token directory: test_data/ (will be created)"; \
	fi
	@echo ""
	@if [ -d "build" ]; then \
		echo "Build directory: build/ (exists)"; \
		echo "Installation status: Installed"; \
		if [ -f "build/bin/softhsm2-util" ]; then \
			echo "SoftHSMv2 version: $$(build/bin/softhsm2-util --version 2>/dev/null | head -1 || echo 'Unknown')"; \
		fi; \
	else \
		echo "Build directory: build/ (not built)"; \
		echo "Installation status: Not installed"; \
	fi

# Platform-specific targets
.PHONY: build-windows build-linux build-macos

build-windows:
	@echo "Building SoftHSMv2 for Windows..."
	@if command -v pwsh >/dev/null 2>&1; then \
		pwsh -File build_softhsm.ps1; \
	elif command -v powershell >/dev/null 2>&1; then \
		powershell -File build_softhsm.ps1; \
	else \
		echo "PowerShell not found. Using bash script..."; \
		./install-softhsmv2.sh; \
	fi

build-linux: build

build-macos: build

# Development targets
.PHONY: dev-setup dev-clean dev-test

dev-setup:
	@echo "Setting up development environment..."
	@chmod +x *.sh
	@echo "Scripts made executable."

dev-clean: clean
	@echo "Performing deep clean..."
	@rm -rf temp_build build test_build
	@echo "All build artifacts removed."

dev-test: test
	@echo "Running additional development tests..."
	@bash -n install-softhsmv2.sh && echo "✓ install-softhsmv2.sh syntax OK"
	@bash -n check_dependencies.sh && echo "✓ check_dependencies.sh syntax OK"
	@bash -n test_build_script.sh && echo "✓ test_build_script.sh syntax OK"
