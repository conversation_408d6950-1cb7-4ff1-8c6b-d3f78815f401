.TH SOFTHSM2-MIGRATE 1 "20 April 2016" "SoftHSM"
.SH NAME
softhsm2-migrate \- SoftHSM v1 migration tool
.SH SYNOPSIS
.PP
.B softhsm2-migrate \-\-db
.I path
.B \-\-token
.I label
.RB [ \-\-pin
.I PIN
.B \-\-no\-public\-key]
.SH DESCRIPTION
.B softhsm2-migrate
is a tool that can migrate SoftHSM v1 databases to PKCS#11.
The default HSM is SoftHSM v2, but can be used with other 
PKCS#11 libraries by using the option
.B \-\-module
.LP
.SH OPTIONS
.TP
.B \-\-db \fIpath\fR
The SoftHSM v1 database that is going to be migrated.
The location of the token database can be found in
the configuration file for SoftHSM v1.
.TP
.B \-\-help\fR, \fB\-h\fR
Show the help information.
.TP
.B \-\-module \fIpath\fR
Use another PKCS#11 library than SoftHSM.
.TP
.B \-\-no\-public\-key
Do not migrate the public key.
.TP
.B \-\-pin \fIPIN\fR
The
.I PIN
for the normal user.
.TP
.B \-\-serial \fInumber\fR
Will use the token with a matching serial number.
.TP
.B \-\-slot \fInumber\fR
The database will be migrated to this slot.
.TP
.B \-\-token \fIlabel\fR
Will use the token with a matching token label.
.TP
.B \-\-version\fR, \fB\-v\fR
Show the version info.
.SH EXAMPLE
.LP
A token database can be migrated with the following command:
.LP
.RS
.nf
softhsm2-migrate \-\-db /home/<USER>/token.db \-\-token mytoken
.fi
.RE
.SH AUTHORS
Written by Rickard Bellgrim, Francis Dupont, René Post, and Roland van Rijswijk.
.LP
.SH "SEE ALSO"
.IR softhsm2-keyconv (1),
.IR softhsm2-util (1),
.IR softhsm2.conf (5)
