﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|@PLATFORM@">
      <Configuration>Debug</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|@PLATFORM@">
      <Configuration>Release</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{07E03E0B-C525-4A72-88C6-2238896A4D8C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>cryptotest</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
	<PlatformToolset>@PLATFORMTOOLSET@</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
	<PlatformToolset>@PLATFORMTOOLSET@</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..;..\..\src\lib;..\..\src\lib\crypto;..\..\src\lib\common;..\..\src\lib\pkcs11;..\..\src\lib\data_mgr;..\..\src\lib\object_store;..\..\src\lib\session_mgr;..\..\src\lib\slot_mgr;..\..\src\lib\win32;@CUINCPATH@;@DEBUGINCPATH@;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>@RUNTIMELIBRARY@</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\@PLATFORMDIR@$(Configuration);@CULIBPATH@;@DEBUGLIBPATH@;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>convarch.lib;cppunitd.lib;@BOTAN_LIBRARY@;@EXTRALIBS@%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..;..\..\src\lib;..\..\src\lib\crypto;..\..\src\lib\common;..\..\src\lib\pkcs11;..\..\src\lib\data_mgr;..\..\src\lib\object_store;..\..\src\lib\session_mgr;..\..\src\lib\slot_mgr;..\..\src\lib\win32;@CUINCPATH@;@INCLUDEPATH@;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>@RUNTIMELIBRARY@</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>..\@PLATFORMDIR@$(Configuration);@CULIBPATH@;@LIBPATH@;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>convarch.lib;cppunit.lib;@BOTAN_LIBRARY@;@EXTRALIBS@%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\src\lib\pkcs11\cryptoki.h" />
    <ClInclude Include="..\..\src\lib\pkcs11\pkcs11.h" />
    <ClInclude Include="..\..\src\lib\pkcs11\pkcs11f.h" />
    <ClInclude Include="..\..\src\lib\pkcs11\pkcs11t.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\AESTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\DESTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\DHTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\DSATests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\ECDHTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\ECDSATests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\ent.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\GOSTTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\HashTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\iso8859.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\MacTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\randtest.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\RNGTests.h" />
    <ClInclude Include="..\..\src\lib\crypto\test\RSATests.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\lib\crypto\test\AESTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\chisq.c" />
    <ClCompile Include="..\..\src\lib\crypto\test\cryptotest.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\DESTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\DHTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\DSATests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\ECDHTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\ECDSATests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\ent.c" />
    <ClCompile Include="..\..\src\lib\crypto\test\GOSTTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\HashTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\iso8859.c" />
    <ClCompile Include="..\..\src\lib\crypto\test\MacTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\randtest.c" />
    <ClCompile Include="..\..\src\lib\crypto\test\RNGTests.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\test\RSATests.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
