/*
 * Copyright (c) 2010 SURFnet bv
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
 * IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
 * IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*****************************************************************************
 RFC4880.h

 Implements a secure password-based key derivation scheme. It is not a generic
 implementation of the RFC but only generates 256-bit AES keys according to
 the "iterated and salted" scheme.
 *****************************************************************************/

#ifndef _SOFTHSM_V2_RFC4880_H
#define _SOFTHSM_V2_RFC4880_H

#include "config.h"
#include "ByteString.h"
#include "log.h"
#include "AESKey.h"

// This define sets the base PBE iteration count; the lowest byte of the salt is added
// to this value as a form of jitter
#define PBE_ITERATION_BASE_COUNT	1500

namespace RFC4880
{
	// This function derives a 256-bit AES key from the supplied password data
	bool PBEDeriveKey(const ByteString& password, ByteString& salt, AESKey** ppKey);
}

#endif // !_SOFTHSM_V2_RFC4880_H

