﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Lib Header Files">
      <UniqueIdentifier>{8440d7eb-5530-4f5e-a355-a43435742c60}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Lib Source Files">
      <UniqueIdentifier>{3c33d54e-4bd1-43e0-bcc7-0d6adcfd5dc7}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Other Header Files">
      <UniqueIdentifier>{ff435d2e-c67a-4f47-9731-28d88617e559}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Other Source Files">
      <UniqueIdentifier>{5df8b0a3-ecc7-4876-aea2-8421c0846535}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\src\lib\test\AsymEncryptDecryptTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\AsymWrapUnwrapTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\DeriveTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\DigestTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\InfoTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\InitTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\ObjectTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\RandomTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\SessionTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\SignVerifyTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\SymmetricAlgorithmTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\TestsBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\TestsNoPINInitBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\TokenTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\test\UserTests.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\common\osmutex.h">
      <Filter>Other Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\win32\setenv.h">
      <Filter>Other Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\access.h">
      <Filter>Lib Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\pkcs11\cryptoki.h">
      <Filter>Lib Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\pkcs11\pkcs11.h">
      <Filter>Other Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\pkcs11\pkcs11f.h">
      <Filter>Other Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\pkcs11\pkcs11t.h">
      <Filter>Other Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\P11Attributes.h">
      <Filter>Lib Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\P11Objects.h">
      <Filter>Lib Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\lib\SoftHSM.h">
      <Filter>Lib Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\lib\test\AsymEncryptDecryptTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\AsymWrapUnwrapTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\DeriveTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\DigestTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\InfoTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\InitTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\ObjectTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\RandomTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\SessionTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\SignVerifyTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\SymmetricAlgorithmTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\TestsBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\TestsNoPINInitBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\TokenTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\UserTests.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\common\osmutex.cpp">
      <Filter>Other Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\win32\setenv.cpp">
      <Filter>Other Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\access.cpp">
      <Filter>Lib Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\main.cpp">
      <Filter>Lib Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\P11Attributes.cpp">
      <Filter>Lib Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\P11Objects.cpp">
      <Filter>Lib Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\SoftHSM.cpp">
      <Filter>Lib Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\test\p11test.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
