﻿
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual C++ Express 2010
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "softhsm2", "softhsm2\softhsm2.vcxproj", "{801F5AB2-7A62-4085-B129-D15E2D717219}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "convarch", "convarch\convarch.vcxproj", "{F64541B6-FFBF-4368-B93A-A5CA8ADAD795}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "keyconv", "keyconv\keyconv.vcxproj", "{9B003E52-F02A-47EA-9942-2D9AE8738161}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "util", "util\util.vcxproj", "{05901466-4184-47C8-9D6C-3BB99BBF5378}"
	ProjectSection(ProjectDependencies) = postProject
		{801F5AB2-7A62-4085-B129-D15E2D717219} = {801F5AB2-7A62-4085-B129-D15E2D717219}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "dump", "dump\dump.vcxproj", "{F60ACB12-7D05-4A89-B2D1-DD16E1F3566B}"
EndProject
@IF TESTS
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "p11test", "p11test\p11test.vcxproj", "{7C5EE7FC-B5FC-47BF-8164-A452FE689472}"
	ProjectSection(ProjectDependencies) = postProject
		{801F5AB2-7A62-4085-B129-D15E2D717219} = {801F5AB2-7A62-4085-B129-D15E2D717219}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "cryptotest", "cryptotest\cryptotest.vcxproj", "{07E03E0B-C525-4A72-88C6-2238896A4D8C}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "datamgrtest", "datamgrtest\datamgrtest.vcxproj", "{E20315B5-B49E-46D7-B7EC-1A439F347C95}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "handlemgrtest", "handlemgrtest\handlemgrtest.vcxproj", "{014B1E10-EC68-4BEC-B992-F92CA2B6816F}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "objstoretest", "objstoretest\objstoretest.vcxproj", "{44F77533-A4A1-4175-8C4C-07106B3F9C08}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sessionmgrtest", "sessionmgrtest\sessionmgrtest.vcxproj", "{45E2ABF6-91A7-4AA5-A82B-0C8E54BCCCB9}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "slotmgrtest", "slotmgrtest\slotmgrtest.vcxproj", "{F62E31E5-0F8D-4B70-8F26-44AFA1A9E645}"
	ProjectSection(ProjectDependencies) = postProject
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795} = {F64541B6-FFBF-4368-B93A-A5CA8ADAD795}
	EndProjectSection
EndProject
@END TESTS
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|@PLATFORM@ = Debug|@PLATFORM@
		Release|@PLATFORM@ = Release|@PLATFORM@
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{801F5AB2-7A62-4085-B129-D15E2D717219}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{801F5AB2-7A62-4085-B129-D15E2D717219}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{801F5AB2-7A62-4085-B129-D15E2D717219}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{801F5AB2-7A62-4085-B129-D15E2D717219}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{F64541B6-FFBF-4368-B93A-A5CA8ADAD795}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{9B003E52-F02A-47EA-9942-2D9AE8738161}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{9B003E52-F02A-47EA-9942-2D9AE8738161}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{9B003E52-F02A-47EA-9942-2D9AE8738161}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{9B003E52-F02A-47EA-9942-2D9AE8738161}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{05901466-4184-47C8-9D6C-3BB99BBF5378}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{05901466-4184-47C8-9D6C-3BB99BBF5378}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{05901466-4184-47C8-9D6C-3BB99BBF5378}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{05901466-4184-47C8-9D6C-3BB99BBF5378}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{F60ACB12-7D05-4A89-B2D1-DD16E1F3566B}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{F60ACB12-7D05-4A89-B2D1-DD16E1F3566B}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{F60ACB12-7D05-4A89-B2D1-DD16E1F3566B}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{F60ACB12-7D05-4A89-B2D1-DD16E1F3566B}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
@IF TESTS
		{7C5EE7FC-B5FC-47BF-8164-A452FE689472}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{7C5EE7FC-B5FC-47BF-8164-A452FE689472}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{7C5EE7FC-B5FC-47BF-8164-A452FE689472}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{7C5EE7FC-B5FC-47BF-8164-A452FE689472}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{07E03E0B-C525-4A72-88C6-2238896A4D8C}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{07E03E0B-C525-4A72-88C6-2238896A4D8C}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{07E03E0B-C525-4A72-88C6-2238896A4D8C}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{07E03E0B-C525-4A72-88C6-2238896A4D8C}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{E20315B5-B49E-46D7-B7EC-1A439F347C95}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{E20315B5-B49E-46D7-B7EC-1A439F347C95}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{E20315B5-B49E-46D7-B7EC-1A439F347C95}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{E20315B5-B49E-46D7-B7EC-1A439F347C95}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{014B1E10-EC68-4BEC-B992-F92CA2B6816F}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{014B1E10-EC68-4BEC-B992-F92CA2B6816F}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{014B1E10-EC68-4BEC-B992-F92CA2B6816F}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{014B1E10-EC68-4BEC-B992-F92CA2B6816F}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{44F77533-A4A1-4175-8C4C-07106B3F9C08}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{44F77533-A4A1-4175-8C4C-07106B3F9C08}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{44F77533-A4A1-4175-8C4C-07106B3F9C08}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{44F77533-A4A1-4175-8C4C-07106B3F9C08}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{45E2ABF6-91A7-4AA5-A82B-0C8E54BCCCB9}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{45E2ABF6-91A7-4AA5-A82B-0C8E54BCCCB9}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{45E2ABF6-91A7-4AA5-A82B-0C8E54BCCCB9}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{45E2ABF6-91A7-4AA5-A82B-0C8E54BCCCB9}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
		{F62E31E5-0F8D-4B70-8F26-44AFA1A9E645}.Debug|@PLATFORM@.ActiveCfg = Debug|@PLATFORM@
		{F62E31E5-0F8D-4B70-8F26-44AFA1A9E645}.Debug|@PLATFORM@.Build.0 = Debug|@PLATFORM@
		{F62E31E5-0F8D-4B70-8F26-44AFA1A9E645}.Release|@PLATFORM@.ActiveCfg = Release|@PLATFORM@
		{F62E31E5-0F8D-4B70-8F26-44AFA1A9E645}.Release|@PLATFORM@.Build.0 = Release|@PLATFORM@
@END TESTS
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
