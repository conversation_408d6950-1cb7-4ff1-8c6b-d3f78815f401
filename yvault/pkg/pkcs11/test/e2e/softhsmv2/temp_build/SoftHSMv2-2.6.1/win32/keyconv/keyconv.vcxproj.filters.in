﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Common Header Files">
      <UniqueIdentifier>{6f8944db-01c2-47c3-a4b4-265d91e99ba0}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Common Source Files">
      <UniqueIdentifier>{b6a2e68c-2518-456b-8592-561c011e0390}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Win32 Source Files">
      <UniqueIdentifier>{14914ba7-3ec3-4f58-a83a-4596a7f52075}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Win32 Header Files">
      <UniqueIdentifier>{3253c2c0-ca7a-4902-8b31-87ab6c4c754f}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\src\bin\common\getpw.h">
      <Filter>Common Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\bin\common\library.h">
      <Filter>Common Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\bin\keyconv\softhsm2-keyconv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
@IF OPENSSL
    <ClInclude Include="..\..\src\lib\crypto\OSSLComp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
@END OPENSSL
    <ClInclude Include="..\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\bin\keyconv\softhsm2-keyconv.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
@IF BOTAN
    <ClCompile Include="..\..\src\bin\keyconv\softhsm2-keyconv-botan.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
@END BOTAN
@IF OPENSSL
    <ClCompile Include="..\..\src\bin\keyconv\softhsm2-keyconv-ossl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\lib\crypto\OSSLComp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
@END OPENSSL
    <ClCompile Include="..\..\src\bin\common\getpw.cpp">
      <Filter>Common Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\bin\common\library.cpp">
      <Filter>Common Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\bin\keyconv\base64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\bin\win32\getpassphase.cpp">
      <Filter>Win32 Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
