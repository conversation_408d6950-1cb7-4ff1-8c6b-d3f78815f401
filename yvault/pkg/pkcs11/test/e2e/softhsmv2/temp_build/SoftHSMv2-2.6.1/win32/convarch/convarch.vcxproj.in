﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|@PLATFORM@">
      <Configuration>Debug</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|@PLATFORM@">
      <Configuration>Release</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\src\lib\common\Configuration.h" />
    <ClInclude Include="..\..\src\lib\common\fatal.h" />
    <ClInclude Include="..\..\src\lib\common\HandleFactory.h" />
    <ClInclude Include="..\..\src\lib\common\log.h" />
    <ClInclude Include="..\..\src\lib\common\MutexFactory.h" />
    <ClInclude Include="..\..\src\lib\common\osmutex.h" />
    <ClInclude Include="..\..\src\lib\common\Serialisable.h" />
    <ClInclude Include="..\..\src\lib\common\SimpleConfigLoader.h" />
    <ClInclude Include="..\..\src\lib\crypto\AESKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\AsymmetricAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\AsymmetricKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\AsymmetricParameters.h" />
@IF BOTAN
    <ClInclude Include="..\..\src\lib\crypto\BotanAES.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanCryptoFactory.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDES.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDH.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDHKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDHPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDHPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDSA.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDSAKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDSAPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanDSAPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDH.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDHKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDHPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDHPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDSA.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDSAKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDSAPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanECDSAPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanGOST.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanGOSTKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanGOSTPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanGOSTPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanGOSTR3411.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanHashAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanMAC.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanMacAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanMD5.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanRNG.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanRSA.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanRSAKeyPair.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanRSAPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanRSAPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanSHA1.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanSHA224.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanSHA256.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanSHA384.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanSHA512.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanSymmetricAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\BotanUtil.h" />
    <ClInclude Include="..\..\src\lib\crypto\Botan_ecb.h" />
    <ClInclude Include="..\..\src\lib\crypto\Botan_rounding.h" />
@END BOTAN
    <ClInclude Include="..\..\src\lib\crypto\CryptoFactory.h" />
    <ClInclude Include="..\..\src\lib\crypto\DerUtil.h" />
    <ClInclude Include="..\..\src\lib\crypto\DESKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\DHParameters.h" />
    <ClInclude Include="..\..\src\lib\crypto\DHPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\DHPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\DSAParameters.h" />
    <ClInclude Include="..\..\src\lib\crypto\DSAPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\DSAPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\ECParameters.h" />
    <ClInclude Include="..\..\src\lib\crypto\ECPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\ECPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\GOSTPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\GOSTPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\HashAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\MacAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\odd.h" />
@IF OPENSSL
     <ClInclude Include="..\..\src\lib\crypto\OSSLAES.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLComp.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLCryptoFactory.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDES.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDH.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDHKeyPair.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDHPrivateKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDHPublicKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDSA.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDSAKeyPair.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDSAPrivateKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLDSAPublicKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLECDH.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLECDSA.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLECKeyPair.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLECPrivateKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLECPublicKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLEVPHashAlgorithm.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLEVPCMacAlgorithm.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLEVPMacAlgorithm.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLEVPSymmetricAlgorithm.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLGOST.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLGOSTKeyPair.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLGOSTPrivateKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLGOSTPublicKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLGOSTR3411.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLCMAC.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLHMAC.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLMD5.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLRNG.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLRSA.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLRSAKeyPair.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLRSAPrivateKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLRSAPublicKey.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLSHA1.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLSHA224.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLSHA256.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLSHA384.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLSHA512.h" />
     <ClInclude Include="..\..\src\lib\crypto\OSSLUtil.h" />
@END OPENSSL
    <ClInclude Include="..\..\src\lib\crypto\PrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\PublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\RNG.h" />
    <ClInclude Include="..\..\src\lib\crypto\RSAParameters.h" />
    <ClInclude Include="..\..\src\lib\crypto\RSAPrivateKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\RSAPublicKey.h" />
    <ClInclude Include="..\..\src\lib\crypto\SymmetricAlgorithm.h" />
    <ClInclude Include="..\..\src\lib\crypto\SymmetricKey.h" />
    <ClInclude Include="..\..\src\lib\data_mgr\ByteString.h" />
    <ClInclude Include="..\..\src\lib\data_mgr\RFC4880.h" />
    <ClInclude Include="..\..\src\lib\data_mgr\salloc.h" />
    <ClInclude Include="..\..\src\lib\data_mgr\SecureAllocator.h" />
    <ClInclude Include="..\..\src\lib\data_mgr\SecureDataManager.h" />
    <ClInclude Include="..\..\src\lib\data_mgr\SecureMemoryRegistry.h" />
    <ClInclude Include="..\..\src\lib\handle_mgr\Handle.h" />
    <ClInclude Include="..\..\src\lib\handle_mgr\HandleManager.h" />
    <ClInclude Include="..\..\src\lib\object_store\Directory.h" />
    <ClInclude Include="..\..\src\lib\object_store\File.h" />
    <ClInclude Include="..\..\src\lib\object_store\FindOperation.h" />
    <ClInclude Include="..\..\src\lib\object_store\Generation.h" />
    <ClInclude Include="..\..\src\lib\object_store\ObjectFile.h" />
    <ClInclude Include="..\..\src\lib\object_store\ObjectStore.h" />
    <ClInclude Include="..\..\src\lib\object_store\ObjectStoreToken.h" />
    <ClInclude Include="..\..\src\lib\object_store\OSAttribute.h" />
    <ClInclude Include="..\..\src\lib\object_store\OSAttributes.h" />
    <ClInclude Include="..\..\src\lib\object_store\OSObject.h" />
    <ClInclude Include="..\..\src\lib\object_store\OSPathSep.h" />
    <ClInclude Include="..\..\src\lib\object_store\OSToken.h" />
    <ClInclude Include="..\..\src\lib\object_store\SessionObject.h" />
    <ClInclude Include="..\..\src\lib\object_store\SessionObjectStore.h" />
    <ClInclude Include="..\..\src\lib\object_store\UUID.h" />
    <ClInclude Include="..\..\src\lib\session_mgr\Session.h" />
    <ClInclude Include="..\..\src\lib\session_mgr\SessionManager.h" />
    <ClInclude Include="..\..\src\lib\slot_mgr\Slot.h" />
    <ClInclude Include="..\..\src\lib\slot_mgr\SlotManager.h" />
    <ClInclude Include="..\..\src\lib\slot_mgr\Token.h" />
    <ClInclude Include="..\..\src\lib\win32\syslog.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\lib\common\Configuration.cpp" />
    <ClCompile Include="..\..\src\lib\common\fatal.cpp" />
    <ClCompile Include="..\..\src\lib\common\log.cpp" />
    <ClCompile Include="..\..\src\lib\common\MutexFactory.cpp" />
    <ClCompile Include="..\..\src\lib\common\osmutex.cpp" />
    <ClCompile Include="..\..\src\lib\common\SimpleConfigLoader.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\AESKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\AsymmetricAlgorithm.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\AsymmetricKeyPair.cpp" />
@IF BOTAN
    <ClCompile Include="..\..\src\lib\crypto\BotanAES.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanCryptoFactory.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDES.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDH.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDHKeyPair.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDHPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDHPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDSA.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDSAKeyPair.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDSAPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanDSAPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDH.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDHKeyPair.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDHPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDHPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDSA.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDSAKeyPair.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDSAPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanECDSAPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanGOST.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanGOSTKeyPair.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanGOSTPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanGOSTPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanGOSTR3411.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanHashAlgorithm.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanMAC.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanMacAlgorithm.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanMD5.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanRNG.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanRSA.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanRSAKeyPair.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanRSAPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanRSAPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanSHA1.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanSHA224.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanSHA256.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanSHA384.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanSHA512.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanSymmetricAlgorithm.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\BotanUtil.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\Botan_ecb.cpp" />
@END BOTAN
    <ClCompile Include="..\..\src\lib\crypto\CryptoFactory.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DerUtil.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DESKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DHParameters.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DHPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DHPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DSAParameters.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DSAPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\DSAPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\ECParameters.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\ECPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\ECPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\GOSTPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\GOSTPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\HashAlgorithm.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\MacAlgorithm.cpp" />
@IF OPENSSL
     <ClCompile Include="..\..\src\lib\crypto\OSSLAES.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLComp.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLCryptoFactory.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDES.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDH.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDHKeyPair.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDHPrivateKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDHPublicKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDSA.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDSAKeyPair.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDSAPrivateKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLDSAPublicKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLECDH.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLECDSA.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLECKeyPair.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLECPrivateKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLECPublicKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLEVPHashAlgorithm.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLEVPCMacAlgorithm.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLEVPMacAlgorithm.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLEVPSymmetricAlgorithm.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLGOST.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLGOSTKeyPair.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLGOSTPrivateKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLGOSTPublicKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLGOSTR3411.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLCMAC.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLHMAC.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLMD5.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLRNG.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLRSA.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLRSAKeyPair.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLRSAPrivateKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLRSAPublicKey.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLSHA1.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLSHA224.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLSHA256.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLSHA384.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLSHA512.cpp" />
     <ClCompile Include="..\..\src\lib\crypto\OSSLUtil.cpp" />
@END OPENSSL
    <ClCompile Include="..\..\src\lib\crypto\RSAParameters.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\RSAPrivateKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\RSAPublicKey.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\SymmetricAlgorithm.cpp" />
    <ClCompile Include="..\..\src\lib\crypto\SymmetricKey.cpp" />
    <ClCompile Include="..\..\src\lib\data_mgr\ByteString.cpp" />
    <ClCompile Include="..\..\src\lib\data_mgr\RFC4880.cpp" />
    <ClCompile Include="..\..\src\lib\data_mgr\salloc.cpp" />
    <ClCompile Include="..\..\src\lib\data_mgr\SecureDataManager.cpp" />
    <ClCompile Include="..\..\src\lib\data_mgr\SecureMemoryRegistry.cpp" />
    <ClCompile Include="..\..\src\lib\handle_mgr\Handle.cpp" />
    <ClCompile Include="..\..\src\lib\handle_mgr\HandleManager.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\Directory.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\File.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\FindOperation.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\Generation.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\ObjectFile.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\ObjectStore.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\ObjectStoreToken.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\OSAttribute.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\OSToken.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\SessionObject.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\SessionObjectStore.cpp" />
    <ClCompile Include="..\..\src\lib\object_store\UUID.cpp" />
    <ClCompile Include="..\..\src\lib\session_mgr\Session.cpp" />
    <ClCompile Include="..\..\src\lib\session_mgr\SessionManager.cpp" />
    <ClCompile Include="..\..\src\lib\slot_mgr\Slot.cpp" />
    <ClCompile Include="..\..\src\lib\slot_mgr\SlotManager.cpp" />
    <ClCompile Include="..\..\src\lib\slot_mgr\Token.cpp" />
    <ClCompile Include="..\..\src\lib\win32\syslog.cpp" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F64541B6-FFBF-4368-B93A-A5CA8ADAD795}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>convarch</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
	<PlatformToolset>@PLATFORMTOOLSET@</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
	<PlatformToolset>@PLATFORMTOOLSET@</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..;..\..\src\lib;..\..\src\lib\pkcs11;..\..\src\lib\common;..\..\src\lib\object_store;..\..\src\lib\slot_mgr;..\..\src\lib\session_mgr;..\..\src\lib\handle_mgr;..\..\src\lib\crypto;..\..\src\lib\win32;..\..\src\lib\data_mgr;@DEBUGINCPATH@;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>@RUNTIMELIBRARY@</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..;..\..\src\lib;..\..\src\lib\pkcs11;..\..\src\lib\common;..\..\src\lib\object_store;..\..\src\lib\slot_mgr;..\..\src\lib\session_mgr;..\..\src\lib\handle_mgr;..\..\src\lib\crypto;..\..\src\lib\win32;..\..\src\lib\data_mgr;@INCLUDEPATH@;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>@RUNTIMELIBRARY@</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
